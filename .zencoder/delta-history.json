{"snapshots": {"/home/<USER>/StudioProjects/BUGS FIXED BMPMS/BodyMount-PMS/src/app/src/main/java/com/bodymount/app/sensor/BluetoothManager.kt": {"filePath": "/home/<USER>/StudioProjects/BUGS FIXED BMPMS/BodyMount-PMS/src/app/src/main/java/com/bodymount/app/sensor/BluetoothManager.kt", "baseContent": "package com.bodymount.app.sensor\n\nimport android.annotation.SuppressLint\nimport android.bluetooth.BluetoothAdapter\nimport android.bluetooth.BluetoothDevice\nimport android.bluetooth.BluetoothGatt\nimport android.bluetooth.BluetoothGattCallback\nimport android.bluetooth.BluetoothGattCharacteristic\nimport android.bluetooth.BluetoothGattDescriptor\nimport android.bluetooth.BluetoothGattService\nimport android.bluetooth.BluetoothProfile\nimport android.content.BroadcastReceiver\nimport android.content.Context\nimport android.content.Intent\nimport android.content.IntentFilter\nimport android.util.Log\nimport android.widget.Toast\nimport com.bodymount.app.common.CommonDataArea\nimport com.bodymount.app.common.CommonDataArea.Companion.bluetoothDevice\nimport com.bodymount.app.common.CommonDataArea.Companion.bluetoothGatt\nimport com.bodymount.app.common.CommonDataArea.Companion.bluetoothManager\nimport com.bodymount.app.common.CommonDataArea.Companion.isConnected\nimport com.bodymount.app.ui.dialogs.SensorListDialogManager\nimport com.bodymount.app.ui.dialogs.dialogHelper.SensorListItems\nimport com.bodymount.app.ui.dialogs.dialogHelper.SensorRecyclerViewAdapter\nimport com.bodymount.app.util.UUIDConfig\nimport kotlinx.coroutines.CompletableDeferred\nimport kotlinx.coroutines.CoroutineExceptionHandler\nimport kotlinx.coroutines.CoroutineScope\nimport kotlinx.coroutines.Dispatchers\nimport kotlinx.coroutines.delay\nimport kotlinx.coroutines.launch\nimport java.io.IOException\nimport java.nio.BufferUnderflowException\nimport java.nio.ByteBuffer\nimport java.nio.ByteOrder\nimport java.util.*\n\n@SuppressLint(\"MissingPermission\")\nclass BluetoothManager(\n    private val context: Context,\n    private val sensorItemsList: MutableList<SensorListItems>,\n    private val adapter: SensorRecyclerViewAdapter,\n    private val sensorListDialogManager: SensorListDialogManager,\n) {\n    private val bluetoothAdapter: BluetoothAdapter? by lazy { BluetoothAdapter.getDefaultAdapter() }\n    private val reconnectionManager = BleReconnectionManager(context, this)\n    private val discoveredDeviceAddresses = mutableSetOf<String>()\n    var data: ByteArray? = null\n\n    //  private var bluetoothGatt: BluetoothGatt? = null\n\n    //reconnection\n    private var isReconnecting = false\n    private var retryCount = 0\n    private val maxRetries = 20\n    private val retryDelay = 5000L\n\n    // CoroutineScope to launch coroutines\n    private val reconnectionScope = CoroutineScope(Dispatchers.IO)\n    private val TAG = \"BMPMS_BLE\"\n\n    fun startBluetoothDiscovery(context: Context) {\n        sensorListDialogManager.showLoadingIndicator()\n        val discoveredDevices = mutableListOf<BluetoothDevice>()\n        if (bluetoothAdapter?.isEnabled == true) {\n            discoveredDevices.clear()\n            val filter = IntentFilter(BluetoothDevice.ACTION_FOUND)\n            context.registerReceiver(this.bluetoothReceiver, filter)\n            // Start discovery\n            bluetoothAdapter?.startDiscovery()\n        } else {\n            // Handle case when Bluetooth is not enabled\n            showToast(\"Bluetooth is not enabled\")\n        }\n    }\n\n    private fun showToast(message: String) {\n\n        Toast.makeText(CommonDataArea.curActivity, message, Toast.LENGTH_SHORT).show()\n\n    }\n\n    private val bluetoothReceiver = object : BroadcastReceiver() {\n        override fun onReceive(context: Context?, intent: Intent?) {\n\n            when (intent?.action) {\n                BluetoothDevice.ACTION_FOUND -> {\n                    val device =\n                        intent.getParcelableExtra<BluetoothDevice>(BluetoothDevice.EXTRA_DEVICE)\n                    val rssi = intent.getShortExtra(BluetoothDevice.EXTRA_RSSI, Short.MIN_VALUE)\n                    device?.let {\n                        handleDiscoveredBluetoothDevice(device, rssi)\n                    }\n                }\n            }\n        }\n    }\n\n    @SuppressLint(\"MissingPermission\", \"NotifyDataSetChanged\")\n    private fun handleDiscoveredBluetoothDevice(device: BluetoothDevice, rssi: Short) {\n        val deviceName = device.name ?: \"Unknown Device\"\n        val sharedPreferences = context.getSharedPreferences(\"settings\", Context.MODE_PRIVATE)\n        val currentDevice = sharedPreferences.getString(\"device_name\", CommonDataArea.BodyMountPMS)\n\n        if (discoveredDeviceAddresses.contains(device.address)) {\n            Log.d(\"BluetoothDiscovery\", \"Device already discovered: ${device.name}, MAC: ${device.address}\")\n            return // Skip if the device has already been processed\n        }\n\n        val isTargetDevice = when (currentDevice) {\n            CommonDataArea.BodyMountPMS -> CommonDataArea.targetMacAddresses.contains(device.address)\n            CommonDataArea.iEcgPatch -> deviceName.contains(CommonDataArea.iEcgPatch)\n            else -> false\n        }\n\n        if (isTargetDevice) {\n            processDiscoveredDevice(device, rssi)\n        } else {\n            Log.d(\n                \"BluetoothDiscovery\",\n                \"Discovered unexpected device: $deviceName, MAC: ${device.address}, RSSI: $rssi dBm\"\n            )\n        }\n    }\n    @SuppressLint(\"NotifyDataSetChanged\")\n    private fun processDiscoveredDevice(device: BluetoothDevice, rssi: Short) {\n        sensorListDialogManager.hideLoadingIndicator()\n        Log.d(\n            \"BluetoothDiscovery\",\n            \"Discovered target device: ${device.name ?: \"Unknown Device\"}, MAC: ${device.address}, RSSI: $rssi dBm\"\n        )\n\n        // Add device to discovered list\n        discoveredDeviceAddresses.add(device.address)\n\n        // Create and populate sensor list item\n        val sensorListItem = SensorListItems(device).apply {\n            setName(device.name ?: \"Unknown Device\")\n            setAddress(device.address)\n            setSignal(rssi.toDouble())\n            // setConnectLoading(R.drawable.wifi_loading) // Uncomment if required\n        }\n        // Add to sensor list and notify adapter\n        sensorItemsList.add(sensorListItem)\n        adapter.notifyDataSetChanged()\n    }\n\n\n    @SuppressLint(\"MissingPermission\")\n    suspend fun connectToDevice(device: BluetoothDevice): Boolean {\n\n        val connectionResult = CompletableDeferred<Boolean>()\n        bluetoothGatt = device.connectGatt(context, false, gattCallBack(connectionResult))\n\n        CommonDataArea.connectedDevices.add(device)\n\n        return connectionResult.await()\n    }\n\n    private fun bondDevice(device: BluetoothDevice): Boolean {\n        return try {\n            val bondMethod = device.javaClass.getMethod(\"createBond\")\n            bondMethod.invoke(device) as Boolean\n        } catch (e: Exception) {\n            e.printStackTrace()\n            false\n        }\n    }\n\n    @SuppressLint(\"MissingPermission\")\n    fun isDeviceBonded(device: BluetoothDevice): Boolean {\n        return device.bondState == BluetoothDevice.BOND_BONDED\n    }\n    private fun gattCallBack(connectionResult: CompletableDeferred<Boolean>) =\n        object : BluetoothGattCallback() {\n            override fun onConnectionStateChange(gatt: BluetoothGatt?, status: Int, newState: Int) {\n                if (newState == BluetoothProfile.STATE_CONNECTED) {\n                    Log.i(TAG, \"DeviceConnected\")\n                    gatt?.let { requestMtu(it, 517) }\n                    connectionResult.complete(true)\n                    CommonDataArea.deviceDisconnectionStatus = false\n                    retryCount = 0\n\n                    // Log successful connection for debugging\n                    com.bodymount.app.common.LogWriter.writeLog(\"Device Connection\", \"Device successfully connected\")\n\n                    // Hide \"No Device Connected\" text when device connects and start chart updates\n                    CoroutineScope(Dispatchers.Main).launch {\n                        CommonDataArea.curActivity?.mainActivityUIs?.hideNoDeviceConnectedText()\n                        // Start chart update coroutine only when device is actually connected\n                        CommonDataArea.curActivity?.startChartUpdateCoRoutine()\n                    }\n                } else if (newState == BluetoothProfile.STATE_DISCONNECTED) {\n                    Log.i(TAG, \"DeviceDisconnected - Status: $status\")\n                    connectionResult.complete(false)\n\n                    // Set disconnection status immediately\n                    CommonDataArea.deviceDisconnectionStatus = true\n\n                    // Log disconnection event for debugging\n                    com.bodymount.app.common.LogWriter.writeLog(\"Device Disconnection\", \"Device disconnected with status: $status\")\n\n                    CoroutineScope(Dispatchers.Main).launch {\n                        // Clear UI components and reset values\n                        CommonDataArea.curActivity?.mainActivityUIs?.clearOldChartComponents()\n                        CommonDataArea.curActivity?.mainActivityUIs?.resetValues()\n                        // Show no device connected text\n                        CommonDataArea.curActivity?.mainActivityUIs?.showNoDeviceConnectedText()\n\n                        com.bodymount.app.common.LogWriter.writeLog(\"UI Update\", \"UI cleared due to device disconnection\")\n                    }\n\n                    //scheduleReconnection()\n                    //retryReconnection(gatt?.device)\n                    retryReconnection(CommonDataArea.bluetoothDevice)\n                }\n            }\n\n            // Define service and characteristic UUIDs\n            val serviceToCharacteristicsMap = mapOf(\n                UUID.fromString(UUIDConfig.serviceToCharacteristicsMap[\"SERVICE_1\"]) to listOf(\n                    UUID.fromString(UUIDConfig.characteristicMap[\"CHARACTERISTIC_DEVICE_INFO\"])\n                ),\n                UUID.fromString(UUIDConfig.serviceToCharacteristicsMap[\"SERVICE_2\"]) to listOf(\n                    UUID.fromString(UUIDConfig.characteristicMap[\"CHARACTERISTIC_ECG_ONE\"]),\n                    UUID.fromString(UUIDConfig.characteristicMap[\"CHARACTERISTIC_ECG_TWO\"]),\n                    UUID.fromString(UUIDConfig.characteristicMap[\"CHARACTERISTIC_ECG_THREE\"])\n                ),\n                UUID.fromString(UUIDConfig.serviceToCharacteristicsMap[\"SERVICE_3\"]) to listOf(\n                    UUID.fromString(UUIDConfig.characteristicMap[\"CHARACTERISTIC_PPG\"]),\n                    UUID.fromString(UUIDConfig.characteristicMap[\"CHARACTERISTIC_SPO2\"])\n                ),\n                UUID.fromString(UUIDConfig.serviceToCharacteristicsMap[\"SERVICE_4\"]) to listOf(\n                    UUID.fromString(UUIDConfig.characteristicMap[\"CHARACTERISTIC_TEMP\"]),\n                    UUID.fromString(UUIDConfig.characteristicMap[\"CHARACTERISTIC_BATTERY\"])\n                ),\n                UUID.fromString(UUIDConfig.serviceToCharacteristicsMap[\"SERVICE_5\"]) to listOf(\n                    UUID.fromString(UUIDConfig.characteristicMap[\"CHARACTERISTIC_SOS\"]),\n                    UUID.fromString(UUIDConfig.characteristicMap[\"CHARACTERISTIC_LEAD_OFF\"])\n                ),\n                UUID.fromString(UUIDConfig.serviceToCharacteristicsMap[\"SERVICE_BP\"]) to listOf(\n                    UUID.fromString(UUIDConfig.characteristicMap[\"CHARACTERISTIC_BMPMS_BP\"]),\n                    UUID.fromString(UUIDConfig.characteristicMap[\"CHARACTERISTIC_BMPMS_BP_STATUS\"]),\n                    UUID.fromString(UUIDConfig.characteristicMap[\"CHARACTERISTIC_BMPMS_ACTIVITY\"])\n                ),\n                UUID.fromString(UUIDConfig.serviceToCharacteristicsMap[\"SERVICE_ACTIVITY\"]) to listOf(\n                    UUID.fromString(UUIDConfig.characteristicMap[\"CHARACTERISTIC_ACTIVITY_1\"]),\n                    UUID.fromString(UUIDConfig.characteristicMap[\"CHARACTERISTIC_ACTIVITY_2\"]),\n                    UUID.fromString(UUIDConfig.characteristicMap[\"CHARACTERISTIC_ACTIVITY_3\"])\n                )\n            )\n\n            private var currentServiceIndex = 0\n            private var currentCharacteristicIndex = 0\n            private val servicesUUIDs = serviceToCharacteristicsMap.keys.toList()\n            private lateinit var characteristicUUIDs: List<UUID>\n\n            private fun setupNextCharacteristic(\n                gatt: BluetoothGatt?,\n                service: BluetoothGattService\n            ) {\n                if (currentCharacteristicIndex >= characteristicUUIDs.size) {\n                    Log.i(TAG, \"All characteristics set up for service: ${service.uuid}\")\n                    currentServiceIndex++\n                    setupNextService(gatt)\n                    return\n                }\n\n                val uuid = characteristicUUIDs[currentCharacteristicIndex]\n                val characteristic = service.getCharacteristic(uuid)\n                characteristic?.let { char ->\n                    val canNotify = char.properties and BluetoothGattCharacteristic.PROPERTY_NOTIFY\n                    if (canNotify != 0) {\n                        val descriptor =\n                            char.getDescriptor(UUID.fromString(\"00002902-0000-1000-8000-00805f9b34fb\"))\n                        descriptor?.let { desc ->\n                            desc.value = BluetoothGattDescriptor.ENABLE_NOTIFICATION_VALUE\n                            Log.i(\"BLE\", \"Writing descriptor for characteristic: $uuid\")\n                            gatt?.writeDescriptor(desc)\n                        }\n                    } else {\n                        Log.i(TAG, \"Characteristic $uuid does not support notification\")\n                        currentCharacteristicIndex++\n                        setupNextCharacteristic(gatt, service)\n                    }\n                } ?: run {\n                    Log.i(TAG, \"Characteristic $uuid not found in service ${service.uuid}\")\n                    currentCharacteristicIndex++\n                    setupNextCharacteristic(gatt, service)\n                }\n            }\n\n            private fun setupNextService(gatt: BluetoothGatt?) {\n                if (currentServiceIndex >= servicesUUIDs.size) {\n                    Log.i(TAG, \"All services and characteristics set up\")\n                    return\n                }\n\n                val serviceUUID = servicesUUIDs[currentServiceIndex]\n                val service = gatt?.getService(serviceUUID)\n                service?.let {\n                    characteristicUUIDs = serviceToCharacteristicsMap[serviceUUID] ?: listOf()\n                    currentCharacteristicIndex = 0\n                    setupNextCharacteristic(gatt, service)\n                } ?: run {\n                    Log.i(TAG, \"Service $serviceUUID not found\")\n                    currentServiceIndex++\n                    setupNextService(gatt)\n                }\n            }\n\n            override fun onServicesDiscovered(gatt: BluetoothGatt?, status: Int) {\n                if (status == BluetoothGatt.GATT_SUCCESS) {\n                    Log.i(TAG, \"ServiceDiscovered\")\n                    setupNextService(gatt)\n                } else {\n                    Log.i(TAG, \"FailedToDiscoverServices: $status\")\n                }\n            }\n\n            override fun onDescriptorWrite(gatt: BluetoothGatt?, descriptor: BluetoothGattDescriptor?, status: Int) {\n                if (status == BluetoothGatt.GATT_SUCCESS) {\n                    val characteristic = descriptor?.characteristic\n                    gatt?.setCharacteristicNotification(characteristic, true)\n                    Log.i(TAG, \"Notification enabled for characteristic: ${characteristic?.uuid}\")\n                    currentCharacteristicIndex++\n                    characteristic?.let { setupNextCharacteristic(gatt, it.service) }\n                } else {\n                    Log.e(\n                        TAG,\n                        \"Failed to write descriptor for ${descriptor?.characteristic?.uuid}, status: $status\"\n                    )\n                }\n            }\n\n            // Define a map to associate UUIDs with processing functions\n            val characteristicHandlers = mapOf<UUID, (ByteArray) -> Unit>(\n                UUID.fromString(UUIDConfig.characteristicMap[\"CHARACTERISTIC_DEVICE_INFO\"]) to { CommonDataArea.bluetoothDataParser.parseBmpmsDeviceInfo(it) },\n                UUID.fromString(UUIDConfig.characteristicMap[\"CHARACTERISTIC_ECG_ONE\"]) to { CommonDataArea.bluetoothDataParser.processECGOneData(it) },\n                UUID.fromString(UUIDConfig.characteristicMap[\"CHARACTERISTIC_ECG_TWO\"]) to { CommonDataArea.bluetoothDataParser.processECGTwoData(it) },\n                UUID.fromString(UUIDConfig.characteristicMap[\"CHARACTERISTIC_ECG_THREE\"]) to { CommonDataArea.bluetoothDataParser.processECGThreeData(it) },\n                UUID.fromString(UUIDConfig.characteristicMap[\"CHARACTERISTIC_PPG\"]) to { CommonDataArea.bluetoothDataParser.processReceivedPPGData(it) },\n                UUID.fromString(UUIDConfig.characteristicMap[\"CHARACTERISTIC_SPO2\"]) to { CommonDataArea.bluetoothDataParser.parseSpo2Data(it) },\n                UUID.fromString(UUIDConfig.characteristicMap[\"CHARACTERISTIC_TEMP\"]) to { CommonDataArea.bluetoothDataParser.parseTempData(it) },\n                UUID.fromString(UUIDConfig.characteristicMap[\"CHARACTERISTIC_BATTERY\"]) to { CommonDataArea.bluetoothDataParser.parseBatteryLevelData(it) },\n                UUID.fromString(UUIDConfig.characteristicMap[\"CHARACTERISTIC_SOS\"]) to { CommonDataArea.bluetoothDataParser.parseSosData(it) },\n                UUID.fromString(UUIDConfig.characteristicMap[\"CHARACTERISTIC_LEAD_OFF\"]) to { CommonDataArea.bluetoothDataParser.leadOffData(it) },\n                UUID.fromString(UUIDConfig.characteristicMap[\"CHARACTERISTIC_ACTIVITY_1\"]) to { CommonDataArea.bluetoothDataParser.parseiRhythmEcgData(it) },\n                UUID.fromString(UUIDConfig.characteristicMap[\"CHARACTERISTIC_ACTIVITY_2\"]) to { CommonDataArea.bluetoothDataParser.parseiRhythmRRData(it) },\n                UUID.fromString(UUIDConfig.characteristicMap[\"CHARACTERISTIC_ACTIVITY_3\"]) to { CommonDataArea.bluetoothDataParser.parseiRhythmHealthData(it) },\n                UUID.fromString(UUIDConfig.characteristicMap[\"CHARACTERISTIC_BMPMS_BP\"]) to { CommonDataArea.bluetoothDataParser.parseBMPMSBPParam(it) },\n                UUID.fromString(UUIDConfig.characteristicMap[\"CHARACTERISTIC_BMPMS_BP_STATUS\"]) to { CommonDataArea.bluetoothDataParser.parseBMPMSBPSTATUSParam(it) },\n                UUID.fromString(UUIDConfig.characteristicMap[\"CHARACTERISTIC_BMPMS_ACTIVITY\"]) to { CommonDataArea.bluetoothDataParser.parseBMPMSActivityParam(it) }\n            )\n\n            // Handle characteristic changes\n            override fun onCharacteristicChanged(gatt: BluetoothGatt, characteristic: BluetoothGattCharacteristic, value: ByteArray) {\n                characteristicHandlers[characteristic.uuid]?.invoke(value)\n                    ?: Log.w(TAG, \"No handler found for characteristic: ${characteristic.uuid}\")\n            }\n\n            override fun onMtuChanged(gatt: BluetoothGatt, mtu: Int, status: Int) {\n                if (status == BluetoothGatt.GATT_SUCCESS) {\n                    Log.i(TAG, \"MTU changed to $mtu\")\n                    gatt?.discoverServices()\n                } else {\n                    Log.w(TAG, \"MTU change unsuccessful: $status\")\n                }\n            }\n        }\n\n    private fun requestMtu(gatt: BluetoothGatt, mtu: Int) {\n        gatt.requestMtu(mtu)\n    }\n    fun receiveAndProcessData() {\n        val receivedBuffer = ByteArray(268) // Adjust the buffer size as needed\n        val receivedData: ByteArray = data ?: return\n\n        val coroutineExceptionHandler = CoroutineExceptionHandler { _, throwable ->\n            // Handle exceptions here\n            Log.e(\"CoroutineExceptionHandler\", \"Exception: ${throwable.stackTraceToString()}\")\n            //   bluetoothDevice?.let { handleDisconnection(it) }\n        }\n\n        val coroutineScope = CoroutineScope(Dispatchers.IO + coroutineExceptionHandler)\n\n        coroutineScope.launch {\n            try {\n                while (isConnected) {\n                    // processInputStream(receivedData)\n                }\n            } catch (e: Exception) {\n                Log.e(\"receiveAndProcessData\", \"Coroutine exception: ${e.message}\")\n                //  bluetoothDevice?.let { handleDisconnection(it) }\n            }\n        }\n    }\n\n\n    fun disconnect() {\n\n        for (macAddress in CommonDataArea.targetMacAddresses) {\n            if (discoveredDeviceAddresses.contains(macAddress)) {\n                discoveredDeviceAddresses.remove(macAddress)\n                // Handle disconnection-related tasks such as notifying the user or attempting reconnection.\n                CommonDataArea.expectedDisconnect = true\n                // context.unregisterReceiver(this.bluetoothReceiver)\n\n            }\n        }\n        bluetoothDevice?.let { handleDisconnection(it) }\n\n        CommonDataArea.manageSensorsDialog?.dismiss()\n    }\n\n//    private fun handleDisconnectionDueToError(e: IOException) {\n//        Log.e(\"bt_socket_closed\", \"Bluetooth socket closed or error: ${e.message}\")\n//        bluetoothDevice?.let { handleDisconnection(it) }\n//    }\n\n    fun removeSensorName() {\n        bluetoothDevice?.let {\n            CommonDataArea.bluetoothConnectionHelper.enrollSensorNameByType(\n                context,\n                \"\",\n                it\n            )\n        }\n        CommonDataArea.deviceModelNumber = null\n        isConnected = false\n\n    }\n\n    fun handleDisconnection(device: BluetoothDevice) {\n        try {\n            // Close the Bluetooth socket to release resources\n//            CommonDataArea.bluetoothSocket.inputStream?.close()\n//            CommonDataArea.bluetoothSocket.close()\n            Log.d(\"managerObj\", \"postUiInitActions:$bluetoothManager \")\n\n            bluetoothGatt?.let {\n                it.disconnect()\n                it.close()\n                // bluetoothGatt = null\n                isConnected = false\n\n                Log.i(TAG, \"DeviceDisconnected from\")\n            }\n            CommonDataArea.connectedDevices.remove(bluetoothDevice)\n            CommonDataArea.bluetoothDevice = null\n            stopBluetoothDiscovery()\n\n            //Reconnection\n            if (!isConnected && !CommonDataArea.expectedDisconnect) {\n                // scheduleReconnection()\n            } else {\n                Log.d(\n                    TAG,\n                    \"Cant Reconnect |isConnected:$isConnected | expectedDisconnect:${CommonDataArea.expectedDisconnect}\"\n                )\n            }\n\n            removeSensorName()\n\n        } catch (e: IOException) {\n            // Handle any exceptions that occur while closing the socket\n            e.printStackTrace()\n            showToast(\"Disconnecting ${e.stackTraceToString()}\")\n        } finally {\n            // Assuming isConnected is a Boolean flag indicating the connection status\n            Log.d(\"handleDisconnection\", \"Bluetooth disconnected\")\n\n            // Use runOnUiThread to show the Toast message on the UI thread\n            CoroutineScope(Dispatchers.Main).launch {\n                CommonDataArea.curActivity?.mainActivityUIs?.clearChartComponents()\n                CommonDataArea.curActivity?.mainActivityUIs?.showNoDeviceConnectedText()\n                CommonDataArea.curActivity?.mainActivityUIs?.resetValues()\n                CommonDataArea.curActivity?.mainActivityUIs?.clearOldChartComponents()\n                CommonDataArea.curActivity?.dialog?.getPatchDisconnectionDialog(device.name)\n                showToast(\"Device Disconnected\")\n            }\n        }\n    }\n\n    @SuppressLint(\"MissingPermission\")\n    fun stopBluetoothDiscovery() {\n        // Check if BluetoothAdapter is not null and if discovery is in progress\n        if (bluetoothAdapter?.isDiscovering == true) {\n            // Stop the discovery\n            Log.d(\"bluetoothAdapter\", \"stopBluetoothDiscovery: \")\n            bluetoothAdapter?.cancelDiscovery()\n        }\n        // Unregister the receiver\n        //context.unregisterReceiver(bluetoothReceiver)\n        // You can also hide the loading indicator if needed\n        sensorListDialogManager.hideLoadingIndicator()\n    }\n\n    private fun retryReconnection(device: BluetoothDevice?) {\n        if (!isReconnecting && retryCount < maxRetries && CommonDataArea.bluetoothDevice != null) {\n            isReconnecting = true\n            retryCount++\n\n            // Delay before retrying\n            reconnectionScope.launch {\n                delay(retryDelay)\n                Log.i(TAG, \"Retrying connection, attempt $retryCount/$maxRetries\")\n\n                device?.let {\n                    connectToDevice(it) // Retry connection\n                }\n                isReconnecting = false\n            }\n        } else {\n            Log.e(TAG, \"Max retries reached or already reconnecting\")\n        }\n    }\n\n    companion object {\n        public fun writeCharacteristic(characteristicUuid: UUID, value: ByteArray) {\n            val bluetoothGatt = CommonDataArea.bluetoothGatt\n            bluetoothGatt?.let { gatt ->\n                val characteristic = gatt.getService(UUID.fromString(UUIDConfig.serviceToCharacteristicsMap[\"SERVICE_BP\"]))\n                    ?.getCharacteristic(characteristicUuid)\n\n                if (characteristic != null) {\n                    characteristic.value = value\n                    val success = gatt.writeCharacteristic(characteristic)\n                    if (success) {\n                        Log.d(\"BluetoothManager\", \"Data sent successfully to $characteristicUuid\")\n                    } else {\n                        Log.e(\"BluetoothManager\", \"Failed to send data to $characteristicUuid\")\n                    }\n                } else {\n                    Log.e(\"BluetoothManager\", \"Characteristic $characteristicUuid not found\")\n                }\n            } ?: Log.e(\"BluetoothManager\", \"BluetoothGatt is null\")\n        }\n    }\n}\n\n", "baseTimestamp": 1758266791880}}}